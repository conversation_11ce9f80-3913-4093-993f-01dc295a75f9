{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1471", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/vendor-portal-auth-flow.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44279, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44279, "count": 1}], "isBlockCoverage": true}, {"functionName": "VendorAuthService", "ranges": [{"startOffset": 907, "endOffset": 1648, "count": 11}], "isBlockCoverage": true}, {"functionName": "login", "ranges": [{"startOffset": 1651, "endOffset": 2786, "count": 9}, {"startOffset": 1743, "endOffset": 1796, "count": 1}, {"startOffset": 1796, "endOffset": 1852, "count": 8}, {"startOffset": 1852, "endOffset": 1907, "count": 3}, {"startOffset": 1881, "endOffset": 1907, "count": 2}, {"startOffset": 1935, "endOffset": 1988, "count": 1}, {"startOffset": 1988, "endOffset": 2785, "count": 7}], "isBlockCoverage": true}, {"functionName": "refreshToken", "ranges": [{"startOffset": 2789, "endOffset": 3519, "count": 2}, {"startOffset": 2902, "endOffset": 3060, "count": 1}, {"startOffset": 3060, "endOffset": 3110, "count": 0}, {"startOffset": 3110, "endOffset": 3518, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3016, "endOffset": 3040, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateToken", "ranges": [{"startOffset": 3522, "endOffset": 4010, "count": 3}, {"startOffset": 3710, "endOffset": 3932, "count": 1}, {"startOffset": 3932, "endOffset": 4006, "count": 2}], "isBlockCoverage": true}, {"functionName": "logout", "ranges": [{"startOffset": 4013, "endOffset": 4101, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4184, "endOffset": 14573, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4275, "endOffset": 7519, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4358, "endOffset": 7306, "count": 20}, {"startOffset": 4626, "endOffset": 4698, "count": 4}, {"startOffset": 4698, "endOffset": 4797, "count": 16}, {"startOffset": 4797, "endOffset": 4830, "count": 12}, {"startOffset": 4832, "endOffset": 5397, "count": 9}, {"startOffset": 5397, "endOffset": 7300, "count": 7}, {"startOffset": 5429, "endOffset": 5464, "count": 3}, {"startOffset": 5466, "endOffset": 6032, "count": 2}, {"startOffset": 6032, "endOffset": 7300, "count": 5}, {"startOffset": 6063, "endOffset": 6099, "count": 4}, {"startOffset": 6101, "endOffset": 6793, "count": 4}, {"startOffset": 6189, "endOffset": 6198, "count": 3}, {"startOffset": 6235, "endOffset": 6401, "count": 1}, {"startOffset": 6401, "endOffset": 6793, "count": 3}, {"startOffset": 6793, "endOffset": 7300, "count": 1}, {"startOffset": 7160, "endOffset": 7300, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4880, "endOffset": 4904, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4929, "endOffset": 5387, "count": 9}, {"startOffset": 5085, "endOffset": 5215, "count": 7}, {"startOffset": 5215, "endOffset": 5377, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5514, "endOffset": 5538, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5563, "endOffset": 6022, "count": 2}, {"startOffset": 5720, "endOffset": 6012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6448, "endOffset": 6783, "count": 3}, {"startOffset": 6490, "endOffset": 6620, "count": 1}, {"startOffset": 6620, "endOffset": 6773, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7014, "endOffset": 7150, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7331, "endOffset": 7513, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7369, "endOffset": 7505, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7556, "endOffset": 7677, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7612, "endOffset": 7665, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7734, "endOffset": 9355, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7824, "endOffset": 8428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8497, "endOffset": 8887, "count": 1}, {"startOffset": 8685, "endOffset": 8694, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8956, "endOffset": 9349, "count": 1}, {"startOffset": 9147, "endOffset": 9156, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9411, "endOffset": 9913, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9500, "endOffset": 9907, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9969, "endOffset": 11633, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10044, "endOffset": 10799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10869, "endOffset": 11255, "count": 1}, {"startOffset": 11067, "endOffset": 11076, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11325, "endOffset": 11627, "count": 1}, {"startOffset": 11427, "endOffset": 11436, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11691, "endOffset": 12869, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11784, "endOffset": 12425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12496, "endOffset": 12863, "count": 1}, {"startOffset": 12659, "endOffset": 12668, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12920, "endOffset": 13871, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12997, "endOffset": 13865, "count": 1}, {"startOffset": 13756, "endOffset": 13765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13936, "endOffset": 14569, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14035, "endOffset": 14563, "count": 1}], "isBlockCoverage": true}]}]}