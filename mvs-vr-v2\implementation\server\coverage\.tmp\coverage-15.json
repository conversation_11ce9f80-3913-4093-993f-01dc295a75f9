{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1767", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/asset-service.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 414, "endOffset": 518, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 802, "endOffset": 9074, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 863, "endOffset": 1389, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1415, "endOffset": 2778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1467, "endOffset": 2412, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2465, "endOffset": 2772, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2813, "endOffset": 5716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2865, "endOffset": 4310, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4367, "endOffset": 5710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5140, "endOffset": 5308, "count": 1}, {"startOffset": 5271, "endOffset": 5307, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5750, "endOffset": 7434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5797, "endOffset": 7064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7112, "endOffset": 7428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7473, "endOffset": 9070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7530, "endOffset": 8257, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8310, "endOffset": 8653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8694, "endOffset": 9064, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1768", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/services/asset-service.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40889, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 525, "endOffset": 12161, "count": 9}], "isBlockCoverage": true}, {"functionName": "AssetService", "ranges": [{"startOffset": 541, "endOffset": 598, "count": 9}], "isBlockCoverage": true}, {"functionName": "getAsset", "ranges": [{"startOffset": 712, "endOffset": 1141, "count": 2}, {"startOffset": 870, "endOffset": 1014, "count": 1}, {"startOffset": 1014, "endOffset": 1137, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAssetsByVendor", "ranges": [{"startOffset": 1300, "endOffset": 1865, "count": 2}, {"startOffset": 1454, "endOffset": 1503, "count": 1}, {"startOffset": 1564, "endOffset": 1698, "count": 0}, {"startOffset": 1724, "endOffset": 1861, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAssetVersions", "ranges": [{"startOffset": 1979, "endOffset": 2476, "count": 2}, {"startOffset": 2192, "endOffset": 2343, "count": 1}, {"startOffset": 2343, "endOffset": 2472, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLatestAssetVersion", "ranges": [{"startOffset": 2614, "endOffset": 3159, "count": 3}, {"startOffset": 2788, "endOffset": 2921, "count": 1}, {"startOffset": 2921, "endOffset": 2958, "count": 2}, {"startOffset": 2960, "endOffset": 3019, "count": 1}, {"startOffset": 3019, "endOffset": 3155, "count": 0}], "isBlockCoverage": true}, {"functionName": "createAsset", "ranges": [{"startOffset": 3456, "endOffset": 5209, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAssetVersion", "ranges": [{"startOffset": 5588, "endOffset": 6921, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAssetBundle", "ranges": [{"startOffset": 7051, "endOffset": 7510, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAssetBundleWithAssets", "ranges": [{"startOffset": 7658, "endOffset": 8335, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAssetBundle", "ranges": [{"startOffset": 8501, "endOffset": 9215, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAssetBundle", "ranges": [{"startOffset": 9482, "endOffset": 10377, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateBundleHash", "ranges": [{"startOffset": 10530, "endOffset": 10714, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateAssetDelta", "ranges": [{"startOffset": 10958, "endOffset": 12159, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12262, "endOffset": 12290, "count": 9}], "isBlockCoverage": true}]}]}