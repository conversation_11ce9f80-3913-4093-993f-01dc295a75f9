/**
 * VisualEditors Component Unit Tests
 * 
 * Comprehensive tests for the main VisualEditors container component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import VisualEditors from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue';

// Mock child components
const ShowroomLayoutEditorMock = {
  name: 'ShowroomLayoutEditor',
  props: ['vendorId', 'showroomId'],
  template: '<div class="showroom-layout-editor-mock">Showroom Layout Editor</div>',
  emits: ['update']
};

const ProductConfiguratorMock = {
  name: 'ProductConfigurator',
  props: ['vendorId', 'productId'],
  template: '<div class="product-configurator-mock">Product Configurator</div>',
  emits: ['update']
};

const MaterialTextureEditorMock = {
  name: 'MaterialTextureEditor',
  props: ['vendorId', 'materialId'],
  template: '<div class="material-texture-editor-mock">Material Texture Editor</div>',
  emits: ['update']
};

const LightingEditorMock = {
  name: 'LightingEditor',
  props: ['vendorId', 'showroomId'],
  template: '<div class="lighting-editor-mock">Lighting Editor</div>',
  emits: ['update']
};

const AnimationEditorMock = {
  name: 'AnimationEditor',
  props: ['vendorId', 'animationId'],
  template: '<div class="animation-editor-mock">Animation Editor</div>',
  emits: ['update']
};

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockShowrooms = [
  { id: 'showroom-1', name: 'Test Showroom 1', vendor_id: 'vendor-1' },
  { id: 'showroom-2', name: 'Test Showroom 2', vendor_id: 'vendor-1' }
];

const mockProducts = [
  { id: 'product-1', name: 'Test Product 1', vendor_id: 'vendor-1' },
  { id: 'product-2', name: 'Test Product 2', vendor_id: 'vendor-1' }
];

const mockMaterials = [
  { id: 'material-1', name: 'Test Material 1', vendor_id: 'vendor-1' },
  { id: 'material-2', name: 'Test Material 2', vendor_id: 'vendor-1' }
];

const mockAnimations = [
  { id: 'animation-1', name: 'Test Animation 1', vendor_id: 'vendor-1' },
  { id: 'animation-2', name: 'Test Animation 2', vendor_id: 'vendor-1' }
];

describe('VisualEditors Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/showroom_layouts')) {
        return Promise.resolve({ data: { data: mockShowrooms } });
      }
      if (url.includes('/items/products')) {
        return Promise.resolve({ data: { data: mockProducts } });
      }
      if (url.includes('/items/materials')) {
        return Promise.resolve({ data: { data: mockMaterials } });
      }
      if (url.includes('/items/animations')) {
        return Promise.resolve({ data: { data: mockAnimations } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    // Mount component with mocks
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor-1'
      },
      global: {
        components: {
          ShowroomLayoutEditor: ShowroomLayoutEditorMock,
          ProductConfigurator: ProductConfiguratorMock,
          MaterialTextureEditor: MaterialTextureEditorMock,
          LightingEditor: LightingEditorMock,
          AnimationEditor: AnimationEditorMock
        },
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.visual-editors').exists()).toBe(true);
    });

    it('should render the header with title and controls', () => {
      expect(wrapper.find('.visual-editors-title').text()).toBe('Visual Editors');
      expect(wrapper.find('.auto-save-toggle').exists()).toBe(true);
    });

    it('should render all tabs', () => {
      const tabs = wrapper.findAll('.tab-button');
      expect(tabs).toHaveLength(5);
      
      const expectedTabs = [
        'Showroom Layout',
        'Product Config',
        'Materials',
        'Lighting',
        'Animation'
      ];
      
      tabs.forEach((tab, index) => {
        expect(tab.text()).toContain(expectedTabs[index]);
      });
    });

    it('should have the layout tab active by default', () => {
      const activeTab = wrapper.find('.tab-button.active');
      expect(activeTab.text()).toContain('Showroom Layout');
      expect(wrapper.vm.activeTab).toBe('layout');
    });
  });

  describe('Tab Navigation', () => {
    it('should switch tabs when clicked', async () => {
      const productTab = wrapper.findAll('.tab-button')[1];
      await productTab.trigger('click');
      
      expect(wrapper.vm.activeTab).toBe('product');
      expect(productTab.classes()).toContain('active');
    });

    it('should render the correct editor component for each tab', async () => {
      // Test layout tab (default)
      expect(wrapper.findComponent(ShowroomLayoutEditorMock).exists()).toBe(true);
      
      // Test product tab
      await wrapper.vm.setActiveTab('product');
      await wrapper.vm.$nextTick();
      expect(wrapper.findComponent(ProductConfiguratorMock).exists()).toBe(true);
      
      // Test material tab
      await wrapper.vm.setActiveTab('material');
      await wrapper.vm.$nextTick();
      expect(wrapper.findComponent(MaterialTextureEditorMock).exists()).toBe(true);
      
      // Test lighting tab
      await wrapper.vm.setActiveTab('lighting');
      await wrapper.vm.$nextTick();
      expect(wrapper.findComponent(LightingEditorMock).exists()).toBe(true);
      
      // Test animation tab
      await wrapper.vm.setActiveTab('animation');
      await wrapper.vm.$nextTick();
      expect(wrapper.findComponent(AnimationEditorMock).exists()).toBe(true);
    });
  });

  describe('Auto-save Functionality', () => {
    it('should have auto-save enabled by default', () => {
      expect(wrapper.vm.autoSaveEnabled).toBe(true);
    });

    it('should toggle auto-save when checkbox is clicked', async () => {
      const autoSaveCheckbox = wrapper.find('input[type="checkbox"]');
      
      await autoSaveCheckbox.setChecked(false);
      expect(wrapper.vm.autoSaveEnabled).toBe(false);
      
      await autoSaveCheckbox.setChecked(true);
      expect(wrapper.vm.autoSaveEnabled).toBe(true);
    });

    it('should call toggleAutoSave method when checkbox changes', async () => {
      const toggleAutoSaveSpy = vi.spyOn(wrapper.vm, 'toggleAutoSave');
      const autoSaveCheckbox = wrapper.find('input[type="checkbox"]');
      
      await autoSaveCheckbox.setChecked(false);
      expect(toggleAutoSaveSpy).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load data on component mount', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/showroom_layouts')
      );
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/products')
      );
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/materials')
      );
    });

    it('should set loading state during data fetch', () => {
      expect(wrapper.vm.loading).toBeDefined();
    });

    it('should handle API errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      mockApi.get.mockRejectedValueOnce(new Error('API Error'));
      
      await wrapper.vm.loadData();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(VisualEditors.props.vendorId.required).toBe(true);
      expect(VisualEditors.props.vendorId.type).toBe(String);
    });

    it('should pass vendorId to child components', async () => {
      await wrapper.vm.$nextTick();
      
      const showroomEditor = wrapper.findComponent(ShowroomLayoutEditorMock);
      expect(showroomEditor.props('vendorId')).toBe('vendor-1');
    });
  });

  describe('Event Handling', () => {
    it('should handle layout update events', async () => {
      const handleLayoutUpdateSpy = vi.spyOn(wrapper.vm, 'handleLayoutUpdate');
      const showroomEditor = wrapper.findComponent(ShowroomLayoutEditorMock);
      
      await showroomEditor.vm.$emit('update', { id: 'layout-1', name: 'Updated Layout' });
      
      expect(handleLayoutUpdateSpy).toHaveBeenCalledWith({
        id: 'layout-1',
        name: 'Updated Layout'
      });
    });

    it('should handle product update events', async () => {
      await wrapper.vm.setActiveTab('product');
      await wrapper.vm.$nextTick();
      
      const handleProductUpdateSpy = vi.spyOn(wrapper.vm, 'handleProductUpdate');
      const productConfigurator = wrapper.findComponent(ProductConfiguratorMock);
      
      await productConfigurator.vm.$emit('update', { id: 'product-1', name: 'Updated Product' });
      
      expect(handleProductUpdateSpy).toHaveBeenCalledWith({
        id: 'product-1',
        name: 'Updated Product'
      });
    });

    it('should handle material update events', async () => {
      await wrapper.vm.setActiveTab('material');
      await wrapper.vm.$nextTick();
      
      const handleMaterialUpdateSpy = vi.spyOn(wrapper.vm, 'handleMaterialUpdate');
      const materialEditor = wrapper.findComponent(MaterialTextureEditorMock);
      
      await materialEditor.vm.$emit('update', { id: 'material-1', name: 'Updated Material' });
      
      expect(handleMaterialUpdateSpy).toHaveBeenCalledWith({
        id: 'material-1',
        name: 'Updated Material'
      });
    });
  });

  describe('Last Saved Functionality', () => {
    it('should display last saved time when available', async () => {
      wrapper.vm.lastSaved = new Date('2023-01-01T12:00:00Z');
      await wrapper.vm.$nextTick();
      
      const lastSavedElement = wrapper.find('.last-saved');
      expect(lastSavedElement.exists()).toBe(true);
      expect(lastSavedElement.text()).toContain('Last saved:');
    });

    it('should format last saved time correctly', () => {
      wrapper.vm.lastSaved = new Date('2023-01-01T12:00:00Z');
      expect(wrapper.vm.formatLastSaved).toBeDefined();
    });
  });
});
