{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1697", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/directus-supabase-integration.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36112, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36112, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 1030, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1084, "endOffset": 1278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1395, "endOffset": 1593, "count": 9}], "isBlockCoverage": true}, {"functionName": "DirectusSupabaseIntegration", "ranges": [{"startOffset": 1779, "endOffset": 1900, "count": 9}], "isBlockCoverage": true}, {"functionName": "syncVendorData", "ranges": [{"startOffset": 1903, "endOffset": 2691, "count": 1}, {"startOffset": 2109, "endOffset": 2144, "count": 0}, {"startOffset": 2146, "endOffset": 2589, "count": 0}], "isBlockCoverage": true}, {"functionName": "syncAssetData", "ranges": [{"startOffset": 2694, "endOffset": 3542, "count": 1}, {"startOffset": 2894, "endOffset": 2928, "count": 0}, {"startOffset": 2930, "endOffset": 3441, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleAuthSync", "ranges": [{"startOffset": 3545, "endOffset": 4342, "count": 2}, {"startOffset": 3783, "endOffset": 4101, "count": 1}, {"startOffset": 4101, "endOffset": 4149, "count": 0}, {"startOffset": 4151, "endOffset": 4181, "count": 0}, {"startOffset": 4181, "endOffset": 4213, "count": 1}, {"startOffset": 4228, "endOffset": 4237, "count": 1}, {"startOffset": 4247, "endOffset": 4338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4415, "endOffset": 11654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4477, "endOffset": 4625, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 6640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4782, "endOffset": 5732, "count": 1}, {"startOffset": 5329, "endOffset": 5731, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5810, "endOffset": 6061, "count": 1}, {"startOffset": 6055, "endOffset": 6060, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6131, "endOffset": 6634, "count": 1}, {"startOffset": 6628, "endOffset": 6633, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6706, "endOffset": 7966, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6795, "endOffset": 7635, "count": 1}, {"startOffset": 7386, "endOffset": 7634, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7712, "endOffset": 7960, "count": 1}, {"startOffset": 7954, "endOffset": 7959, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8036, "endOffset": 9397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8133, "endOffset": 9089, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9161, "endOffset": 9391, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9456, "endOffset": 10386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9554, "endOffset": 10380, "count": 1}, {"startOffset": 10316, "endOffset": 10379, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleWebhook", "ranges": [{"startOffset": 9854, "endOffset": 10008, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10456, "endOffset": 11650, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10532, "endOffset": 11644, "count": 1}, {"startOffset": 11525, "endOffset": 11643, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10635, "endOffset": 10962, "count": 0}], "isBlockCoverage": false}, {"functionName": "syncWithRetry", "ranges": [{"startOffset": 11118, "endOffset": 11470, "count": 0}], "isBlockCoverage": false}]}]}