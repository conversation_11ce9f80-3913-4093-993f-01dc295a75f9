{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1771", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/frontend-backend-communication.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30130, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30130, "count": 1}], "isBlockCoverage": true}, {"functionName": "GET", "ranges": [{"startOffset": 788, "endOffset": 1209, "count": 2}, {"startOffset": 857, "endOffset": 869, "count": 1}, {"startOffset": 870, "endOffset": 876, "count": 1}, {"startOffset": 998, "endOffset": 1187, "count": 1}, {"startOffset": 1188, "endOffset": 1192, "count": 1}], "isBlockCoverage": true}, {"functionName": "POST", "ranges": [{"startOffset": 1221, "endOffset": 1585, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1279, "endOffset": 1303, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1326, "endOffset": 1577, "count": 2}], "isBlockCoverage": true}, {"functionName": "PATCH", "ranges": [{"startOffset": 1598, "endOffset": 1971, "count": 0}], "isBlockCoverage": false}, {"functionName": "GET", "ranges": [{"startOffset": 2009, "endOffset": 2331, "count": 1}], "isBlockCoverage": true}, {"functionName": "GET", "ranges": [{"startOffset": 2370, "endOffset": 2704, "count": 1}], "isBlockCoverage": true}, {"functionName": "POST", "ranges": [{"startOffset": 2734, "endOffset": 3057, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3142, "endOffset": 9900, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3214, "endOffset": 4156, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3254, "endOffset": 3943, "count": 11}, {"startOffset": 3537, "endOffset": 3609, "count": 2}, {"startOffset": 3609, "endOffset": 3723, "count": 9}, {"startOffset": 3723, "endOffset": 3746, "count": 7}, {"startOffset": 3748, "endOffset": 3797, "count": 7}, {"startOffset": 3797, "endOffset": 3937, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3968, "endOffset": 4150, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4006, "endOffset": 4142, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4193, "endOffset": 4314, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4249, "endOffset": 4302, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4387, "endOffset": 6174, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4470, "endOffset": 4924, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4995, "endOffset": 5504, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5581, "endOffset": 6168, "count": 1}, {"startOffset": 5963, "endOffset": 6167, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6239, "endOffset": 6768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6314, "endOffset": 6762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6834, "endOffset": 7367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6910, "endOffset": 7361, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7432, "endOffset": 8099, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7507, "endOffset": 8093, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8153, "endOffset": 8924, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8229, "endOffset": 8559, "count": 1}, {"startOffset": 8335, "endOffset": 8344, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8625, "endOffset": 8918, "count": 1}, {"startOffset": 8809, "endOffset": 8818, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8990, "endOffset": 9401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9080, "endOffset": 9395, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9456, "endOffset": 9896, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9545, "endOffset": 9890, "count": 1}, {"startOffset": 9785, "endOffset": 9884, "count": 0}], "isBlockCoverage": true}]}]}