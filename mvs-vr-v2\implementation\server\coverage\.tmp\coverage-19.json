{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1769", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/services/scene/scene-flow-simulator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30120, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30120, "count": 1}, {"startOffset": 487, "endOffset": 30119, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 9510, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1770", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/services/scene/scene-flow-simulator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36556, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36556, "count": 1}, {"startOffset": 298, "endOffset": 36555, "count": 0}], "isBlockCoverage": true}, {"functionName": "simulateFlow", "ranges": [{"startOffset": 454, "endOffset": 2505, "count": 0}], "isBlockCoverage": false}, {"functionName": "find<PERSON>llP<PERSON>s", "ranges": [{"startOffset": 2712, "endOffset": 4002, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculatePathMetrics", "ranges": [{"startOffset": 4136, "endOffset": 5145, "count": 0}], "isBlockCoverage": false}, {"functionName": "findDeadEnds", "ranges": [{"startOffset": 5268, "endOffset": 5525, "count": 0}], "isBlockCoverage": false}, {"functionName": "findBottlenecks", "ranges": [{"startOffset": 5670, "endOffset": 7679, "count": 0}], "isBlockCoverage": false}, {"functionName": "findCycles", "ranges": [{"startOffset": 7788, "endOffset": 9648, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9764, "endOffset": 9805, "count": 0}], "isBlockCoverage": false}]}]}