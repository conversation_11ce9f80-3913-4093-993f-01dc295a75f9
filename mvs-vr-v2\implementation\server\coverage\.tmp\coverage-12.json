{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1699", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/api/middleware/api-key-middleware.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56241, "count": 1}, {"startOffset": 1117, "endOffset": 56240, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 375, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 432, "endOffset": 845, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 866, "endOffset": 1016, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1454, "endOffset": 10715, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1700", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/api/middleware/api-key-middleware.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 67400, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 67400, "count": 1}, {"startOffset": 796, "endOffset": 801, "count": 0}, {"startOffset": 861, "endOffset": 866, "count": 0}, {"startOffset": 905, "endOffset": 921, "count": 0}], "isBlockCoverage": true}, {"functionName": "hashApiKey", "ranges": [{"startOffset": 1459, "endOffset": 1557, "count": 0}], "isBlockCoverage": false}, {"functionName": "getApiKeyData", "ranges": [{"startOffset": 1725, "endOffset": 2569, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRateLimited", "ranges": [{"startOffset": 2789, "endOffset": 3286, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackApiKeyUsage", "ranges": [{"startOffset": 3485, "endOffset": 5064, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasRequiredPermissions", "ranges": [{"startOffset": 5298, "endOffset": 6121, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasRequiredScopes", "ranges": [{"startOffset": 6325, "endOffset": 6714, "count": 0}], "isBlockCoverage": false}, {"functionName": "authenticateApi<PERSON>ey", "ranges": [{"startOffset": 7063, "endOffset": 11931, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1765", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/api/middleware/auth-middleware.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19643, "count": 1}, {"startOffset": 996, "endOffset": 1001, "count": 0}, {"startOffset": 1061, "endOffset": 1066, "count": 0}, {"startOffset": 1113, "endOffset": 1139, "count": 0}, {"startOffset": 1192, "endOffset": 1212, "count": 0}, {"startOffset": 1253, "endOffset": 1280, "count": 0}, {"startOffset": 1319, "endOffset": 1335, "count": 0}, {"startOffset": 1620, "endOffset": 1628, "count": 0}, {"startOffset": 1629, "endOffset": 1638, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeKeystore", "ranges": [{"startOffset": 2147, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2900, "endOffset": 2969, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRateLimiter", "ranges": [{"startOffset": 3122, "endOffset": 4554, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackFailedAttempt", "ranges": [{"startOffset": 4635, "endOffset": 5502, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIpBlocked", "ranges": [{"startOffset": 5551, "endOffset": 5733, "count": 0}], "isBlockCoverage": false}, {"functionName": "encryptToken", "ranges": [{"startOffset": 6343, "endOffset": 6799, "count": 0}], "isBlockCoverage": false}, {"functionName": "decryptToken", "ranges": [{"startOffset": 6960, "endOffset": 7283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTokenRevoked", "ranges": [{"startOffset": 7448, "endOffset": 7778, "count": 0}], "isBlockCoverage": false}, {"functionName": "revokeToken", "ranges": [{"startOffset": 7960, "endOffset": 8406, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateDirectusToken", "ranges": [{"startOffset": 8576, "endOffset": 9244, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateSupabaseToken", "ranges": [{"startOffset": 9402, "endOffset": 10009, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshSupabaseToken", "ranges": [{"startOffset": 10177, "endOffset": 11051, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSecureCookie", "ranges": [{"startOffset": 11289, "endOffset": 11613, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTokenFromRequest", "ranges": [{"startOffset": 11774, "endOffset": 12523, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackAuthEvent", "ranges": [{"startOffset": 12662, "endOffset": 13397, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateCsrfToken", "ranges": [{"startOffset": 13497, "endOffset": 13555, "count": 0}], "isBlockCoverage": false}, {"functionName": "authenticate", "ranges": [{"startOffset": 13901, "endOffset": 19119, "count": 0}], "isBlockCoverage": false}]}]}