{"result": [{"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1766", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/api/middleware/api-key-middleware-vitest.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56439, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56439, "count": 1}, {"startOffset": 227, "endOffset": 270, "count": 0}, {"startOffset": 272, "endOffset": 560, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 594, "endOffset": 630, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 687, "endOffset": 1100, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1121, "endOffset": 1271, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1768, "endOffset": 10637, "count": 0}], "isBlockCoverage": false}]}]}